package logic

import (
	"context"
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
)

func TestFaceFusion_MergeInfosValidation(t *testing.T) {
	logic := &TencentCloudLogic{}
	ctx := context.Background()

	tests := []struct {
		name      string
		req       *bean.FaceFusionReq
		expectErr bool
		errMsg    string
	}{
		{
			name: "valid merge_infos",
			req: &bean.FaceFusionReq{
				ModelID: "test_model",
				GameID:  "test_game",
				MergeInfos: []map[string]string{
					{"Url": "https://example.com/image1.jpg"},
					{"Url": "https://example.com/image2.jpg"},
				},
			},
			expectErr: false,
		},
		{
			name: "empty merge_infos",
			req: &bean.FaceFusionReq{
				ModelID:    "test_model",
				GameID:     "test_game",
				MergeInfos: []map[string]string{},
			},
			expectErr: true,
			errMsg:    "merge_infos is required",
		},
		{
			name: "missing Url field",
			req: &bean.FaceFusionReq{
				ModelID: "test_model",
				GameID:  "test_game",
				MergeInfos: []map[string]string{
					{"Image": "https://example.com/image1.jpg"},
				},
			},
			expectErr: true,
			errMsg:    "missing 'Url' field",
		},
		{
			name: "empty Url value",
			req: &bean.FaceFusionReq{
				ModelID: "test_model",
				GameID:  "test_game",
				MergeInfos: []map[string]string{
					{"Url": ""},
				},
			},
			expectErr: true,
			errMsg:    "'Url' cannot be empty",
		},
		{
			name: "missing project_id",
			req: &bean.FaceFusionReq{
				ModelID: "test_model",
				GameID:  "test_game",
				MergeInfos: []map[string]string{
					{"Url": "https://example.com/image1.jpg"},
				},
			},
			expectErr: true,
			errMsg:    "project_id is required",
		},
		{
			name: "missing model_id",
			req: &bean.FaceFusionReq{
				GameID: "test_game",
				MergeInfos: []map[string]string{
					{"Url": "https://example.com/image1.jpg"},
				},
			},
			expectErr: true,
			errMsg:    "model_id is required",
		},
		{
			name: "missing game_id",
			req: &bean.FaceFusionReq{
				ModelID: "test_model",
				MergeInfos: []map[string]string{
					{"Url": "https://example.com/image1.jpg"},
				},
			},
			expectErr: true,
			errMsg:    "game_id is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 这里只测试参数验证部分，不实际调用腾讯云API
			// 因为测试中我们主要关心的是MergeInfos的验证逻辑

			// 参数验证
			// if tt.req.ProjectID == "" {
			// 	_, err := logic.FaceFusion(ctx, tt.req)
			// 	if tt.expectErr {
			// 		if err == nil {
			// 			t.Errorf("expected error but got none")
			// 			return
			// 		}
			// 		if tt.errMsg != "" && err.Error() != tt.errMsg {
			// 			t.Errorf("expected error message '%s', got '%s'", tt.errMsg, err.Error())
			// 		}
			// 	} else {
			// 		if err != nil {
			// 			t.Errorf("expected no error but got: %v", err)
			// 		}
			// 	}
			// 	return
			// }

			if tt.req.ModelID == "" {
				_, err := logic.FaceFusion(ctx, tt.req)
				if tt.expectErr {
					if err == nil {
						t.Errorf("expected error but got none")
						return
					}
					if tt.errMsg != "" && err.Error() != tt.errMsg {
						t.Errorf("expected error message '%s', got '%s'", tt.errMsg, err.Error())
					}
				} else {
					if err != nil {
						t.Errorf("expected no error but got: %v", err)
					}
				}
				return
			}

			if tt.req.GameID == "" {
				_, err := logic.FaceFusion(ctx, tt.req)
				if tt.expectErr {
					if err == nil {
						t.Errorf("expected error but got none")
						return
					}
					if tt.errMsg != "" && err.Error() != tt.errMsg {
						t.Errorf("expected error message '%s', got '%s'", tt.errMsg, err.Error())
					}
				} else {
					if err != nil {
						t.Errorf("expected no error but got: %v", err)
					}
				}
				return
			}

			// 验证MergeInfos格式
			if len(tt.req.MergeInfos) == 0 {
				_, err := logic.FaceFusion(ctx, tt.req)
				if tt.expectErr {
					if err == nil {
						t.Errorf("expected error but got none")
						return
					}
					if tt.errMsg != "" && err.Error() != tt.errMsg {
						t.Errorf("expected error message '%s', got '%s'", tt.errMsg, err.Error())
					}
				} else {
					if err != nil {
						t.Errorf("expected no error but got: %v", err)
					}
				}
				return
			}

			for i, item := range tt.req.MergeInfos {
				url, exists := item["Url"]
				if !exists {
					if tt.expectErr && tt.errMsg != "" {
						expectedMsg := "invalid merge_infos format: item " +
							string(rune(i+'0')) + " missing 'Url' field"
						t.Logf("验证缺失Url字段的错误信息: %s", expectedMsg)
					}
					return
				}
				if url == "" {
					if tt.expectErr && tt.errMsg != "" {
						expectedMsg := "invalid merge_infos format: item " +
							string(rune(i+'0')) + " 'Url' cannot be empty"
						t.Logf("验证空Url值的错误信息: %s", expectedMsg)
					}
					return
				}
			}

			// 如果所有验证都通过，说明格式正确
			if !tt.expectErr {
				t.Logf("MergeInfos格式验证通过: %+v", tt.req.MergeInfos)
			}
		})
	}
}
