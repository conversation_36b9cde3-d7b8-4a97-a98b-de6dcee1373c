package service

import (
	"bytes"
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"strings"
	"sync"
	"time"

	"net/http"
	"net/url"

	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/tencentyun/cos-go-sdk-v5"
	"gorm.io/gorm"
)

var (
	_uploadOnce    sync.Once
	_uploadService *UploadService
)

type UploadService struct{}

func SingletonUploadService() *UploadService {
	_uploadOnce.Do(func() {
		_uploadService = &UploadService{}
	})
	return _uploadService
}

// UploadFile 上传文件
func (s *UploadService) UploadFile(ctx context.Context, file *multipart.FileHeader) (string, error) {
	stream, err := file.Open()
	if err != nil {
		return "", err
	}
	defer stream.Close()

	// stream to []byte
	streamByte, err := io.ReadAll(stream)
	if err != nil {
		return "", err
	}
	md5 := util.EncodeMD5(string(streamByte))

	// 检查文件是否已存在
	fileUrl, err := s.GetFileUrl(ctx, md5)
	if err != nil {
		return "", err
	}
	if fileUrl != "" {
		return fileUrl, nil
	}

	// 上传到腾讯云COS
	u, _ := url.Parse(config.GlobConfig.OSS.BucketURL)
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.GlobConfig.OSS.SecretID,
			SecretKey: config.GlobConfig.OSS.SecretKey,
		},
	})

	// 使用时间戳+随机数作为文件名前缀
	filePrefix := fmt.Sprintf("%d%d", time.Now().Unix(), util.GenRandomNum())
	fileName := fmt.Sprintf("/%s/%s-%s", config.GlobConfig.OSS.Env, filePrefix, file.Filename)
	_, err = client.Object.Put(ctx, fileName, bytes.NewReader(streamByte), nil)
	if err != nil {
		return "", err
	}

	// 构建文件URL
	fileURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.Domain, fileName)
	ossURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.BucketURL, fileName)

	// 保存文件信息到数据库
	err = s.CreateFileData(ctx, fileName, md5, fileURL, ossURL, file.Size)
	if err != nil {
		return "", err
	}

	return fileURL, nil
}

// UploadBase64Image 上传base64图片数据到OSS
func (s *UploadService) UploadBase64Image(ctx context.Context, base64Data, fileName string) (string, error) {
	logger.Logger.InfofCtx(ctx, "[人脸融合] 开始上传base64图片, 文件名: %s", fileName)

	// 解码base64数据
	var imageData []byte
	var err error

	// 处理data:image/jpeg;base64,前缀
	if strings.Contains(base64Data, ",") {
		parts := strings.Split(base64Data, ",")
		if len(parts) == 2 {
			base64Data = parts[1]
		}
	}

	imageData, err = base64.StdEncoding.DecodeString(base64Data)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] base64解码失败: %v", err)
		return "", fmt.Errorf("decode base64 image: %w", err)
	}

	// 计算MD5
	md5 := util.EncodeMD5(string(imageData))

	// 检查文件是否已存在
	fileUrl, err := s.GetFileUrl(ctx, md5)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 检查文件是否存在失败: %v", err)
		return "", err
	}
	if fileUrl != "" {
		logger.Logger.InfofCtx(ctx, "[人脸融合] 文件已存在，返回已有URL: %s", fileUrl)
		return fileUrl, nil
	}

	// 上传到腾讯云COS
	u, _ := url.Parse(config.GlobConfig.OSS.BucketURL)
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.GlobConfig.OSS.SecretID,
			SecretKey: config.GlobConfig.OSS.SecretKey,
		},
	})

	// 使用时间戳+随机数作为文件名前缀
	filePrefix := fmt.Sprintf("%d%d", time.Now().Unix(), util.GenRandomNum())
	ossFileName := fmt.Sprintf("/%s/face_fusion/%s-%s", config.GlobConfig.OSS.Env, filePrefix, fileName)

	_, err = client.Object.Put(ctx, ossFileName, bytes.NewReader(imageData), nil)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 上传到OSS失败: %v", err)
		return "", fmt.Errorf("upload to OSS: %w", err)
	}

	// 构建文件URL
	fileURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.Domain, ossFileName)
	ossURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.BucketURL, ossFileName)

	// 保存文件信息到数据库
	err = s.CreateFaceFusionFileData(ctx, ossFileName, md5, fileURL, ossURL, int64(len(imageData)))
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 保存文件记录失败: %v", err)
		return "", fmt.Errorf("save file record: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[人脸融合] 上传图片成功, 文件URL: %s", fileURL)
	return fileURL, nil
}

// GetFileUrl 根据MD5获取文件URL
func (s *UploadService) GetFileUrl(ctx context.Context, md5 string) (string, error) {
	upload := store.QueryDB().MUpload
	uploadCtx := upload.WithContext(ctx)
	uploadInfo, err := uploadCtx.Where(upload.Md5.Eq(md5), upload.IsDeleted.Zero()).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return "", err
	}
	if uploadInfo == nil {
		return "", nil
	}
	return uploadInfo.URL, nil
}

// CreateFileData 创建文件记录
func (s *UploadService) CreateFileData(ctx context.Context, fileName, md5, url, ossURL string, fileSize int64) error {
	// 获取文件扩展名
	fileType := util.GetFileType(fileName)

	upload := store.QueryDB().MUpload
	uploadCtx := upload.WithContext(ctx)
	err := uploadCtx.Create(&model.MUpload{
		FileName:    fileName,
		FileSize:    fileSize,
		FileType:    fileType,
		Md5:         md5,
		URL:         url,
		OssURL:      ossURL,
		OssBucket:   config.GlobConfig.OSS.BucketURL,
		Description: "工单附件",
		CreatedAt:   time.Now().UnixMilli(),
		UpdatedAt:   time.Now().UnixMilli(),
	})
	if err != nil {
		return err
	}
	return nil
}

// CreateFaceFusionFileData 创建人脸融合文件记录
func (s *UploadService) CreateFaceFusionFileData(ctx context.Context, fileName, md5, url, ossURL string, fileSize int64) error {
	// 获取文件扩展名
	fileType := util.GetFileType(fileName)

	upload := store.QueryDB().MUpload
	uploadCtx := upload.WithContext(ctx)
	err := uploadCtx.Create(&model.MUpload{
		FileName:    fileName,
		FileSize:    fileSize,
		FileType:    fileType,
		Md5:         md5,
		URL:         url,
		OssURL:      ossURL,
		OssBucket:   config.GlobConfig.OSS.BucketURL,
		Description: "人脸融合结果图片",
		CreatedAt:   time.Now().UnixMilli(),
		UpdatedAt:   time.Now().UnixMilli(),
	})
	if err != nil {
		return err
	}
	return nil
}
