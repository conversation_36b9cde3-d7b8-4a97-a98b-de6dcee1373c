package service

import (
	"context"
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
)

func TestFaceFusionWithImageDetection(t *testing.T) {
	// 初始化配置（测试环境）
	config.GlobConfig = &config.Config{
		NetEaseYidun: config.NetEaseYidunConf{
			SecretID:   "test_secret_id",
			SecretKey:  "test_secret_key",
			BusinessID: "test_business_id",
		},
		TencentCloud: config.TencentCloudConf{
			SecretID:  "test_tencent_secret_id",
			SecretKey: "test_tencent_secret_key",
		},
		FaceFusion: config.FaceFusionConf{
			ProjectID:          "test_project",
			DefaultRspImgType:  "base64",
			TemplateURL:        "https://example.com/template",
		},
	}

	ctx := context.Background()

	tests := []struct {
		name        string
		req         *bean.FaceFusionReq
		expectError bool
		description string
	}{
		{
			name: "valid request with image detection",
			req: &bean.FaceFusionReq{
				GameID:  "test_game",
				UserID:  "test_user",
				ModelID: "test_model",
				MergeInfos: []map[string]string{
					{"Url": "https://example.com/user_face.jpg"},
				},
				RspImgType: "base64",
			},
			expectError: true, // 在测试环境中会失败，因为没有真实的API密钥
			description: "包含图片检测的人脸融合请求",
		},
		{
			name: "request without images",
			req: &bean.FaceFusionReq{
				GameID:     "test_game",
				UserID:     "test_user",
				ModelID:    "test_model",
				MergeInfos: []map[string]string{},
				RspImgType: "base64",
			},
			expectError: true, // TencentCloudService未初始化会失败
			description: "不包含图片的人脸融合请求",
		},
		{
			name: "request with invalid images",
			req: &bean.FaceFusionReq{
				GameID:  "test_game",
				UserID:  "test_user",
				ModelID: "test_model",
				MergeInfos: []map[string]string{
					{"Url": "invalid_url"},
					{"Url": "https://example.com/suspicious_image.jpg"},
				},
				RspImgType: "base64",
			},
			expectError: true, // 图片检测会失败
			description: "包含可疑图片的人脸融合请求",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 注意：这里我们测试的是集成流程，不是实际的API调用
			// 在真实环境中，需要配置正确的API密钥和业务ID
			
			service := SingletonTencentCloudService()
			_, err := service.FaceFusion(ctx, tt.req)
			
			if (err != nil) != tt.expectError {
				t.Errorf("FaceFusion() error = %v, expectError %v", err, tt.expectError)
			}
			
			t.Logf("Test case: %s - %s", tt.name, tt.description)
			if err != nil {
				t.Logf("Error (expected in test env): %v", err)
			}
		})
	}
}

func TestImageDetectionIntegration(t *testing.T) {
	// 测试图片检测服务的集成
	config.GlobConfig = &config.Config{
		NetEaseYidun: config.NetEaseYidunConf{
			SecretID:   "test_secret_id",
			SecretKey:  "test_secret_key",
			BusinessID: "test_business_id",
		},
	}

	ctx := context.Background()
	service := SingletonImageDetectionService()

	// 测试不同类型的图片URL
	testCases := []struct {
		name        string
		mergeInfos  []map[string]string
		expectError bool
	}{
		{
			name: "normal images",
			mergeInfos: []map[string]string{
				{"Url": "https://example.com/normal_face.jpg"},
			},
			expectError: true, // API调用会失败，但流程正确
		},
		{
			name: "multiple images",
			mergeInfos: []map[string]string{
				{"Url": "https://example.com/face1.jpg"},
				{"Url": "https://example.com/face2.jpg"},
				{"Url": "https://example.com/face3.jpg"},
			},
			expectError: true, // API调用会失败，但流程正确
		},
		{
			name:        "no images",
			mergeInfos:  []map[string]string{},
			expectError: false, // 没有图片时应该直接通过
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := service.DetectImagesInMergeInfos(ctx, tc.mergeInfos)
			if (err != nil) != tc.expectError {
				t.Errorf("DetectImagesInMergeInfos() error = %v, expectError %v", err, tc.expectError)
			}
			if err != nil {
				t.Logf("Expected error in test environment: %v", err)
			}
		})
	}
}
