package service

import (
	"context"
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/stretchr/testify/assert"
)

func TestImageDetectionService_DetectImagesInMergeInfos(t *testing.T) {
	// 初始化配置（测试环境）
	config.GlobConfig = &config.Config{
		NetEaseYidun: config.NetEaseYidunConf{
			SecretID:   "test_secret_id",
			SecretKey:  "test_secret_key",
			BusinessID: "test_business_id",
		},
	}

	service := SingletonImageDetectionService()
	ctx := context.Background()

	tests := []struct {
		name        string
		mergeInfos  []map[string]string
		expectError bool
		description string
	}{
		{
			name:        "empty merge infos",
			mergeInfos:  []map[string]string{},
			expectError: false,
			description: "空的MergeInfos应该直接返回成功",
		},
		{
			name: "merge infos without URLs",
			mergeInfos: []map[string]string{
				{"Name": "test"},
				{"Type": "image"},
			},
			expectError: false,
			description: "不包含URL的MergeInfos应该直接返回成功",
		},
		{
			name: "merge infos with empty URLs",
			mergeInfos: []map[string]string{
				{"Url": ""},
				{"Url": ""},
			},
			expectError: false,
			description: "包含空URL的MergeInfos应该直接返回成功",
		},
		// Note: 实际的图片检测测试需要真实的配置和网络环境，在测试环境中会失败
		// 这里主要测试URL提取逻辑和基础验证
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.DetectImagesInMergeInfos(ctx, tt.mergeInfos)
			if (err != nil) != tt.expectError {
				t.Errorf("DetectImagesInMergeInfos() error = %v, expectError %v", err, tt.expectError)
			}
			t.Logf("Test case: %s - %s", tt.name, tt.description)
			if err != nil {
				t.Logf("Error (expected in test env): %v", err)
			}
		})
	}
}

func TestImageDetectionService_DetectImages(t *testing.T) {
	// 初始化配置（测试环境）
	config.GlobConfig = &config.Config{
		NetEaseYidun: config.NetEaseYidunConf{
			SecretID:   "test_secret_id",
			SecretKey:  "test_secret_key",
			BusinessID: "test_business_id",
		},
	}

	service := SingletonImageDetectionService()
	ctx := context.Background()

	t.Run("empty image list", func(t *testing.T) {
		err := service.DetectImages(ctx, []string{})
		assert.NoError(t, err)
	})

	t.Run("nil image list", func(t *testing.T) {
		err := service.DetectImages(ctx, nil)
		assert.NoError(t, err)
	})

	// Note: 实际的图片检测测试需要真实的配置和网络环境
	// 这里只测试基本的输入验证逻辑
}

func TestImageDetectionService_URLExtraction(t *testing.T) {
	// 测试URL提取逻辑
	mergeInfos := []map[string]string{
		{"Name": "test1", "Url": "https://example.com/image1.jpg"},
		{"Name": "test2", "Value": "value2"},
		{"Name": "test3", "Url": ""},
		{"Name": "test4", "Url": "https://example.com/image2.jpg"},
	}

	var imageURLs []string
	for _, item := range mergeInfos {
		if url, exists := item["Url"]; exists && url != "" {
			imageURLs = append(imageURLs, url)
		}
	}

	expected := []string{
		"https://example.com/image1.jpg",
		"https://example.com/image2.jpg",
	}
	assert.Equal(t, expected, imageURLs)
}
