package service

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"gorm.io/gorm"
)

var (
	_faceFusionOnce    sync.Once
	_faceFusionService *FaceFusionService
)

type FaceFusionService struct{}

func SingletonFaceFusionService() *FaceFusionService {
	_faceFusionOnce.Do(func() {
		_faceFusionService = &FaceFusionService{}
	})
	return _faceFusionService
}

// CreateFaceFusionRecord 创建人脸融合记录
func (s *FaceFusionService) CreateFaceFusionRecord(ctx context.Context, req *bean.FaceFusionTaskRequest) (*model.MFaceFusion, error) {
	logger.Logger.InfofCtx(ctx, "[人脸融合] 创建数据库记录: task_id=%s, game_id=%s, user_id=%s", req.TaskID, req.GameID, req.UserID)

	// 序列化MergeInfos为JSON
	mergeInfosJSON, err := json.Marshal(req.MergeInfos)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 序列化MergeInfos失败: %v", err)
		return nil, fmt.Errorf("marshal merge infos: %w", err)
	}

	// 转换参数类型
	var fuseFaceDegree int32
	if req.FuseFaceDegree != nil {
		fuseFaceDegree = int32(*req.FuseFaceDegree)
	}

	var fuseProfileDegree int32
	if req.FuseProfileDegree != nil {
		fuseProfileDegree = int32(*req.FuseProfileDegree)
	}

	var logoAdd int32
	if req.LogoAdd != nil {
		logoAdd = int32(*req.LogoAdd)
	}

	now := time.Now().UnixMilli()
	record := &model.MFaceFusion{
		GameID:            req.GameID,
		UserID:            req.UserID,
		TaskID:            req.TaskID,
		ModelID:           req.ModelID,
		ProjectID:         "", // 将在处理时设置
		RequestID:         "",
		ImageURL:          "",
		OssURL:            "",
		Status:            "processing",
		Message:           "Face fusion task submitted",
		MergeInfos:        string(mergeInfosJSON),
		FuseFaceDegree:    fuseFaceDegree,
		FuseProfileDegree: fuseProfileDegree,
		LogoAdd:           logoAdd,
		LogoParam:         req.LogoParam,
		FuseParam:         req.FuseParam,
		RspImgType:        req.RspImgType,
		CreatedAt:         now,
		UpdatedAt:         now,
		ProcessedAt:       0,
	}

	faceFusion := store.QueryDB().MFaceFusion
	faceFusionCtx := faceFusion.WithContext(ctx)

	err = faceFusionCtx.Create(record)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 创建数据库记录失败: %v", err)
		return nil, fmt.Errorf("create face fusion record: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[人脸融合] 创建数据库记录成功: id=%d, task_id=%s", record.ID, record.TaskID)
	return record, nil
}

// UpdateFaceFusionResult 更新人脸融合结果
func (s *FaceFusionService) UpdateFaceFusionResult(ctx context.Context, taskID string, result *bean.FaceFusionTaskResult) error {
	logger.Logger.InfofCtx(ctx, "[人脸融合] 更新处理结果: task_id=%s, status=%s", taskID, result.Status)

	faceFusion := store.QueryDB().MFaceFusion
	faceFusionCtx := faceFusion.WithContext(ctx)

	now := time.Now().UnixMilli()
	updates := map[string]interface{}{
		"project_id":   result.ProjectID,
		"request_id":   result.RequestID,
		"status":       result.Status,
		"message":      result.Message,
		"updated_at":   now,
		"processed_at": now,
	}

	// 如果有融合后的图片，更新图片URL
	if result.FusedImage != "" {
		// 如果是base64格式，上传到OSS
		if result.Status == "success" {
			uploadService := SingletonUploadService()
			fileName := fmt.Sprintf("face_fusion_%s_%d.jpg", taskID, time.Now().Unix())
			imageURL, err := uploadService.UploadBase64Image(ctx, result.FusedImage, fileName)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "[人脸融合] 上传图片失败: %v", err)
				// 不返回错误，继续更新其他字段
				updates["message"] = fmt.Sprintf("Face fusion completed but image upload failed: %v", err)
			} else {
				updates["image_url"] = imageURL
				updates["oss_url"] = imageURL // 在这个实现中，两者相同
				logger.Logger.InfofCtx(ctx, "[人脸融合] 图片上传成功: %s", imageURL)
			}
		}
	}

	_, err := faceFusionCtx.Where(faceFusion.TaskID.Eq(taskID)).Updates(updates)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 更新数据库记录失败: %v", err)
		return fmt.Errorf("update face fusion record: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[人脸融合] 更新处理结果成功: task_id=%s", taskID)
	return nil
}

// GetFaceFusionByTaskID 根据任务ID获取人脸融合记录
func (s *FaceFusionService) GetFaceFusionByTaskID(ctx context.Context, taskID string) (*model.MFaceFusion, error) {
	faceFusion := store.QueryDB().MFaceFusion
	faceFusionCtx := faceFusion.WithContext(ctx)

	record, err := faceFusionCtx.Where(faceFusion.TaskID.Eq(taskID)).First()
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 查询记录失败: task_id=%s, err=%v", taskID, err)
		return nil, fmt.Errorf("get face fusion record: %w", err)
	}

	return record, nil
}

// GetFaceFusionByGameIDAndUserID 根据游戏ID和用户ID获取人脸融合记录列表
func (s *FaceFusionService) GetFaceFusionByGameIDAndUserID(ctx context.Context, gameID, userID string, limit int) ([]*model.MFaceFusion, error) {
	faceFusion := store.QueryDB().MFaceFusion
	faceFusionCtx := faceFusion.WithContext(ctx)

	query := faceFusionCtx.Where(faceFusion.GameID.Eq(gameID), faceFusion.UserID.Eq(userID)).
		Order(faceFusion.CreatedAt.Desc())

	if limit > 0 {
		query = query.Limit(limit)
	}

	records, err := query.Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 查询用户记录失败: game_id=%s, user_id=%s, err=%v", gameID, userID, err)
		return nil, fmt.Errorf("get user face fusion records: %w", err)
	}

	return records, nil
}

// GetFaceFusionByStatus 根据状态获取人脸融合记录列表
func (s *FaceFusionService) GetFaceFusionByStatus(ctx context.Context, status string, limit int) ([]*model.MFaceFusion, error) {
	faceFusion := store.QueryDB().MFaceFusion
	faceFusionCtx := faceFusion.WithContext(ctx)

	query := faceFusionCtx.Where(faceFusion.Status.Eq(status)).
		Order(faceFusion.CreatedAt.Desc())

	if limit > 0 {
		query = query.Limit(limit)
	}

	records, err := query.Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 查询状态记录失败: status=%s, err=%v", status, err)
		return nil, fmt.Errorf("get face fusion records by status: %w", err)
	}

	return records, nil
}

// GetFaceFusionStats 获取人脸融合统计信息
func (s *FaceFusionService) GetFaceFusionStats(ctx context.Context, gameID string) (map[string]int64, error) {
	faceFusion := store.QueryDB().MFaceFusion
	faceFusionCtx := faceFusion.WithContext(ctx)

	stats := make(map[string]int64)

	// 总数
	total, err := faceFusionCtx.Where(faceFusion.GameID.Eq(gameID)).Count()
	if err != nil {
		return nil, fmt.Errorf("count total records: %w", err)
	}
	stats["total"] = total

	// 成功数
	success, err := faceFusionCtx.Where(faceFusion.GameID.Eq(gameID), faceFusion.Status.Eq("success")).Count()
	if err != nil {
		return nil, fmt.Errorf("count success records: %w", err)
	}
	stats["success"] = success

	// 失败数
	failed, err := faceFusionCtx.Where(faceFusion.GameID.Eq(gameID), faceFusion.Status.Eq("failed")).Count()
	if err != nil {
		return nil, fmt.Errorf("count failed records: %w", err)
	}
	stats["failed"] = failed

	// 处理中数
	processing, err := faceFusionCtx.Where(faceFusion.GameID.Eq(gameID), faceFusion.Status.Eq("processing")).Count()
	if err != nil {
		return nil, fmt.Errorf("count processing records: %w", err)
	}
	stats["processing"] = processing

	return stats, nil
}
