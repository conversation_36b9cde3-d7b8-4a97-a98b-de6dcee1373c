package config

import (
	"errors"
	"log"
	"os"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

type Config struct {
	Logger          LoggerConf          `mapstructure:"loggerconf"`
	Server          ServerConf          `mapstructure:"serverconf"`
	Cors            CorsConf            `mapstructure:"corsconf"`
	Mysql           MysqlConf           `mapstructure:"mysqlconf"`
	Redis           RedisConf           `mapstructure:"redisconf"`
	RedisJob        RedisTaskConf       `mapstructure:"redistaskconf"`
	Kafka           KafkaConf           `mapstructure:"kafkaconf"`
	WechatPay       WechatPayConf       `mapstructure:"wechatpayconf"`
	Minigame        MinigameConf        `mapstructure:"minigameconf"`
	GravityEngine   GravityEngineConf   `mapstructure:"gravityengineconf"`
	IpRegion        IpRegionConf        `mapstructure:"ipregionconf"`
	Thinkdata       ThinkdataConf       `mapstructure:"thinkdataconf"`
	<PERSON><PERSON><PERSON>          DouyinConf          `mapstructure:"douyinconf"`
	QQ              QQConf              `mapstructure:"qqconf"`
	Qiyu            QiyuConf            `mapstructure:"qiyuconf"`
	OSS             OSS                 `mapstructure:"oss"`
	TencentCloud    TencentCloudConf    `mapstructure:"tencentcloudconf"`
	Miniprogram     MiniprogramConf     `mapstructure:"miniprogramconf"`
	NetEaseYidun    NetEaseYidunConf    `mapstructure:"neteaseyidunconf"`
	AIModel         AIModelConf         `mapstructure:"aimodelconf"`
	Smtp            SmtpConfig          `mapstructure:"smtpconf"`
	EmailTask       EmailTaskConfig     `mapstructure:"emailtaskconf"`
	ExtraDataReport ExtraDataReportConf `mapstructure:"extradatareportconf"`
	CustomerService CustomerServiceConf `mapstructure:"customerserviceconf"`
	FaceFusion      FaceFusionConf      `mapstructure:"facefusionconf"`
	Pprof           PprofConf           `mapstructure:"pprofconf"`
}

type LoggerConf struct {
	Level      string `mapstructure:"level"`
	FileName   string `mapstructure:"file_name"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxBackups int    `mapstructure:"max_backups"`
	MaxAge     int    `mapstructure:"max_age"`
}

type ServerConf struct {
	JWTSecret        string `mapstructure:"jwt_secret"`
	JWTRefreshSecret string `mapstructure:"jwt_refresh_secret"`
}

type CorsConf struct {
	// AllowOrigin string `mapstructure:"allow_origin"`
	AllowOrigin []string `mapstructure:"allow_origin"`
}

type MysqlConf struct {
	Host             string        `mapstructure:"host"`
	Port             string        `mapstructure:"port"`
	UserName         string        `mapstructure:"user_name"`
	Password         string        `mapstructure:"password"`
	DBName           string        `mapstructure:"db_name"`
	OpenConns        int           `mapstructure:"open_conns"`
	IdleConns        int           `mapstructure:"idle_conns"`
	ConnMaxLifetime  time.Duration `mapstructure:"conn_max_lifetime"`
	ConnMaxIdleTime  time.Duration `mapstructure:"conn_max_idle_time"`
	SlowSQLThreshold int           `mapstructure:"slow_sql_threshold"`
}

type RedisConf struct {
	Type        string   `mapstructure:"type"`
	Hosts       []string `mapstructure:"hosts"`
	DB          int      `mapstructure:"db"`
	UserName    string   `mapstructure:"user_name"`
	Password    string   `mapstructure:"password"`
	PoolSize    int      `mapstructure:"poolsize"`
	MinIdleCons int      `mapstructure:"minidle_cons"`
}

type RedisTaskConf struct {
	Type        string   `mapstructure:"type"`
	Hosts       []string `mapstructure:"hosts"`
	DB          int      `mapstructure:"db"`
	UserName    string   `mapstructure:"user_name"`
	Password    string   `mapstructure:"password"`
	PoolSize    int      `mapstructure:"poolsize"`
	MinIdleCons int      `mapstructure:"minidle_cons"`
}

type KafkaConf struct {
	Addr      []string `mapstructure:"addr"`
	User      string   `mapstructure:"user"`
	Password  string   `mapstructure:"password"`
	GroupName string   `mapstructure:"group_name"`
	Offsets   bool     `mapstructure:"offsets"` // true: 从上次（旧的offsets）开始读取， false：从当前（新的offsets）开始读取
}

type WechatPayConf struct {
	MidasEnv         int32  `mapstructure:"midas_env"`
	BaseURL          string `mapstructure:"base_url"`
	CustomerPayURL   string `mapstructure:"customer_pay_url"`
	H5PayCallbackURL string `mapstructure:"h5_pay_callback_url"`
}

type MinigameConf struct {
	BaseURL string `mapstructure:"base_url"`
}

type GravityEngineConf struct {
	Env     string `mapstructure:"env"`
	BaseURL string `mapstructure:"base_url"`
}

type IpRegionConf struct {
	FilePath string `mapstructure:"file_path"`
}

type ThinkdataConf struct {
	ThinkdataAppID string `mapstructure:"think_data_app_id"`
}

type DouyinConf struct {
	BaseURL    string `mapstructure:"base_url"`
	ToutiaoURL string `mapstructure:"toutiao_url"`
}

type QQConf struct {
	BaseURL string `mapstructure:"base_url"`
}

type QiyuConf struct {
	BaseURL   string `mapstructure:"base_url"`
	AppKey    string `mapstructure:"app_key"`
	AppSecret string `mapstructure:"app_secret"`
	ReturnEnv string `mapstructure:"return_env"`
}

type OSS struct {
	Env       string `mapstructure:"env"`
	Domain    string `mapstructure:"domain"`
	BucketURL string `mapstructure:"bucket_url"`
	SecretID  string `mapstructure:"secret_id"`
	SecretKey string `mapstructure:"secret_key"`
}

type TencentCloudConf struct {
	SecretID         string `mapstructure:"secret_id"`
	SecretKey        string `mapstructure:"secret_key"`
	BizType          string `mapstructure:"biz_type"`
	CaptchaSecretID  string `mapstructure:"captcha_secret_id"`
	CaptchaSecretKey string `mapstructure:"captcha_secret_key"`
}

type MiniprogramConf struct {
	IsRefreshURLLink bool   `mapstructure:"is_refresh_url_link"`
	EnvVersion       string `mapstructure:"env_version"`
}

type NetEaseYidunConf struct {
	SecretID   string `mapstructure:"secret_id"`
	SecretKey  string `mapstructure:"secret_key"`
	BusinessID string `mapstructure:"business_id"`
}

type AIModelConf struct {
	BaseURL string `mapstructure:"base_url"`
	APIKey  string `mapstructure:"api_key"`
	Model   string `mapstructure:"model"`
}

type ExtraDataReportConf struct {
	TargetGameID            string `mapstructure:"target_game_id"`
	TargetThinkingDataAppID string `mapstructure:"target_thinking_data_app_id"`
}

type CustomerServiceConf struct {
	BaseURL string `mapstructure:"base_url"`
}

type FaceFusionConf struct {
	ProjectID   string `mapstructure:"project_id"`
	CallbackURL string `mapstructure:"callback_url"`
	// 模板底图 URL（可选：如果使用固定底图而不是依赖 ModelId 内置模板）
	TemplateURL string `mapstructure:"template_url"`
	// 默认返回图片类型：base64 或 url
	DefaultRspImgType string `mapstructure:"default_rsp_img_type"`
	// 请求超时时间（秒），<=0 则使用默认5秒
	TimeoutSeconds int `mapstructure:"timeout_seconds"`
	// 是否允许同时使用 ModelID 与 TemplateURL（某些场景可能需要二选一）
	AllowModelAndTemplate bool `mapstructure:"allow_model_and_template"`

	// QPS 限制配置
	QPSLimit int `mapstructure:"qps_limit"` // QPS限制，默认18（腾讯云限制20，留2个安全余量）
	// 队列配置
	MaxQueueSize int `mapstructure:"max_queue_size"` // 最大队列大小，默认1000
	// 队列超时时间（秒），任务在队列中等待的最大时间
	QueueTimeoutSeconds int `mapstructure:"queue_timeout_seconds"` // 默认300秒（5分钟）
	// 是否启用QPS限制
	EnableQPSLimit bool `mapstructure:"enable_qps_limit"` // 默认true
}

type PprofConf struct {
	Enabled bool   `mapstructure:"enabled"`
	Port    string `mapstructure:"port"`
}

type DouyinFeedGameConf struct {
	SignSecret string `mapstructure:"sign_secret"` // 抖音信息流游戏签名密钥
	// Enabled        bool   `mapstructure:"enabled"`         // 是否启用抖音信息流游戏功能
	GameSceneAPI   string `mapstructure:"game_scene_api"`  // 游戏场景API地址
	APITimeout     int    `mapstructure:"api_timeout"`     // API超时时间（秒）
	EnableFallback bool   `mapstructure:"enable_fallback"` // 是否启用降级逻辑
}

var (
	GlobConfig     = &Config{}
	DefaultKey     = "Env"
	DefaultEnv     = "local" // 默认环境
	DefaultFileExt = "yaml"
	DefaultPath    = "configs" // TEST: DefaultPath    = "../../configs"
)

func MustInit() {
	env := os.Getenv(DefaultKey)
	if env == "" {
		env = DefaultEnv
	}
	viper.SetConfigName(env)
	viper.SetConfigType(DefaultFileExt)
	viper.AddConfigPath(DefaultPath)

	if err := viper.ReadInConfig(); err != nil {
		var configFileNotFoundError viper.ConfigFileNotFoundError
		if errors.As(err, &configFileNotFoundError) {
			log.Panicf("Config file not found; ignore error if desired: %v\n", err)
		}
	}
	if err := viper.Unmarshal(&GlobConfig); err != nil {
		log.Panicf("Unable to unmarshal config into struct: %v\n", err)
	}

	viper.OnConfigChange(func(e fsnotify.Event) {
		log.Printf("Config file changed: %s", e.Name)
	})
	viper.WatchConfig()
}
