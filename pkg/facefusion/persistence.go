package facefusion

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

// PersistentTask 持久化任务结构
type PersistentTask struct {
	Request     *bean.FaceFusionTaskRequest `json:"request"`
	SubmittedAt time.Time                   `json:"submitted_at"`
	TaskID      string                      `json:"task_id"`
}

// QueuePersistence 队列持久化管理器
type QueuePersistence struct {
	filePath string
	enabled  bool
}

// NewQueuePersistence 创建队列持久化管理器
func NewQueuePersistence(dataDir string, enabled bool) *QueuePersistence {
	if !enabled {
		return &QueuePersistence{enabled: false}
	}

	// 确保数据目录存在
	if err := os.MkdirAll(dataDir, 0755); err != nil {
		if logger.Logger != nil {
			logger.Logger.Errorf("Failed to create data directory %s: %v", dataDir, err)
		}
		return &QueuePersistence{enabled: false}
	}

	filePath := filepath.Join(dataDir, "facefusion_queue.json")
	return &QueuePersistence{
		filePath: filePath,
		enabled:  true,
	}
}

// SaveTasks 保存任务到文件
func (p *QueuePersistence) SaveTasks(tasks []*QueuedTask) error {
	if !p.enabled {
		return nil
	}

	// 转换为持久化格式
	persistentTasks := make([]*PersistentTask, 0, len(tasks))
	for _, task := range tasks {
		persistentTasks = append(persistentTasks, &PersistentTask{
			Request:     task.Request,
			SubmittedAt: task.SubmittedAt,
			TaskID:      task.Request.TaskID,
		})
	}

	// 序列化为JSON
	data, err := json.MarshalIndent(persistentTasks, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal tasks: %w", err)
	}

	// 写入临时文件，然后原子性地重命名
	tempFile := p.filePath + ".tmp"
	if err := os.WriteFile(tempFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write temp file: %w", err)
	}

	if err := os.Rename(tempFile, p.filePath); err != nil {
		os.Remove(tempFile) // 清理临时文件
		return fmt.Errorf("failed to rename temp file: %w", err)
	}

	if logger.Logger != nil {
		logger.Logger.Infof("Saved %d tasks to persistent storage", len(tasks))
	}

	return nil
}

// LoadTasks 从文件加载任务
func (p *QueuePersistence) LoadTasks() ([]*PersistentTask, error) {
	if !p.enabled {
		return nil, nil
	}

	// 检查文件是否存在
	if _, err := os.Stat(p.filePath); os.IsNotExist(err) {
		return nil, nil // 文件不存在，返回空列表
	}

	// 读取文件
	data, err := os.ReadFile(p.filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read persistence file: %w", err)
	}

	// 反序列化
	var persistentTasks []*PersistentTask
	if err := json.Unmarshal(data, &persistentTasks); err != nil {
		return nil, fmt.Errorf("failed to unmarshal tasks: %w", err)
	}

	if logger.Logger != nil {
		logger.Logger.Infof("Loaded %d tasks from persistent storage", len(persistentTasks))
	}

	return persistentTasks, nil
}

// ClearTasks 清除持久化的任务
func (p *QueuePersistence) ClearTasks() error {
	if !p.enabled {
		return nil
	}

	if err := os.Remove(p.filePath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to remove persistence file: %w", err)
	}

	if logger.Logger != nil {
		logger.Logger.Info("Cleared persistent task storage")
	}

	return nil
}

// IsEnabled 检查持久化是否启用
func (p *QueuePersistence) IsEnabled() bool {
	return p.enabled
}

// GetFilePath 获取持久化文件路径
func (p *QueuePersistence) GetFilePath() string {
	return p.filePath
}
