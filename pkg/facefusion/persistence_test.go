package facefusion

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestQueuePersistence(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "facefusion_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// 创建持久化管理器
	persistence := NewQueuePersistence(tempDir, true)
	assert.True(t, persistence.IsEnabled())
	assert.Equal(t, filepath.Join(tempDir, "facefusion_queue.json"), persistence.GetFilePath())

	// 创建测试任务
	tasks := []*QueuedTask{
		{
			Request: &bean.FaceFusionTaskRequest{
				TaskID:  "task-1",
				GameID:  "game-1",
				UserID:  "user-1",
				ModelID: "model-1",
			},
			SubmittedAt: time.Now(),
			ResultChan:  make(chan *TaskResult, 1),
			Context:     context.Background(),
		},
		{
			Request: &bean.FaceFusionTaskRequest{
				TaskID:  "task-2",
				GameID:  "game-2",
				UserID:  "user-2",
				ModelID: "model-2",
			},
			SubmittedAt: time.Now().Add(-1 * time.Minute),
			ResultChan:  make(chan *TaskResult, 1),
			Context:     context.Background(),
		},
	}

	// 保存任务
	err = persistence.SaveTasks(tasks)
	assert.NoError(t, err)

	// 验证文件存在
	_, err = os.Stat(persistence.GetFilePath())
	assert.NoError(t, err)

	// 加载任务
	loadedTasks, err := persistence.LoadTasks()
	assert.NoError(t, err)
	assert.Len(t, loadedTasks, 2)

	// 验证任务内容
	assert.Equal(t, "task-1", loadedTasks[0].TaskID)
	assert.Equal(t, "game-1", loadedTasks[0].Request.GameID)
	assert.Equal(t, "task-2", loadedTasks[1].TaskID)
	assert.Equal(t, "game-2", loadedTasks[1].Request.GameID)

	// 清除任务
	err = persistence.ClearTasks()
	assert.NoError(t, err)

	// 验证文件已删除
	_, err = os.Stat(persistence.GetFilePath())
	assert.True(t, os.IsNotExist(err))

	// 再次加载应该返回空列表
	loadedTasks, err = persistence.LoadTasks()
	assert.NoError(t, err)
	assert.Len(t, loadedTasks, 0)
}

func TestQueuePersistenceDisabled(t *testing.T) {
	// 创建禁用的持久化管理器
	persistence := NewQueuePersistence("", false)
	assert.False(t, persistence.IsEnabled())

	// 创建测试任务
	tasks := []*QueuedTask{
		{
			Request: &bean.FaceFusionTaskRequest{
				TaskID: "task-1",
			},
			SubmittedAt: time.Now(),
			ResultChan:  make(chan *TaskResult, 1),
			Context:     context.Background(),
		},
	}

	// 保存任务应该成功但不做任何事
	err := persistence.SaveTasks(tasks)
	assert.NoError(t, err)

	// 加载任务应该返回nil
	loadedTasks, err := persistence.LoadTasks()
	assert.NoError(t, err)
	assert.Nil(t, loadedTasks)

	// 清除任务应该成功但不做任何事
	err = persistence.ClearTasks()
	assert.NoError(t, err)
}

func TestQueuePersistenceInvalidDirectory(t *testing.T) {
	// 尝试在无效目录创建持久化管理器
	persistence := NewQueuePersistence("/invalid/path/that/does/not/exist", true)
	assert.False(t, persistence.IsEnabled())
}
