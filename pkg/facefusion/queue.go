package facefusion

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

// QPSLimitedQueue QPS限制队列
type QPSLimitedQueue struct {
	// 配置
	qpsLimit            int  // QPS限制
	maxQueueSize        int  // 最大队列大小
	queueTimeoutSeconds int  // 队列超时时间
	enableQPSLimit      bool // 是否启用QPS限制

	// 队列状态
	queue           chan *QueuedTask // 任务队列
	activeRequests  int              // 当前活跃请求数
	lastRequestTime time.Time        // 上次请求时间
	mutex           sync.RWMutex     // 读写锁

	// 统计信息
	totalProcessed int64 // 总处理数量
	totalQueued    int64 // 总排队数量
	totalTimeout   int64 // 总超时数量
	totalRejected  int64 // 总拒绝数量
}

// QueuedTask 队列中的任务
type QueuedTask struct {
	Request     *bean.FaceFusionTaskRequest // 任务请求
	SubmittedAt time.Time                   // 提交时间
	ResultChan  chan *TaskResult            // 结果通道
	Context     context.Context             // 上下文
}

// TaskResult 任务结果
type TaskResult struct {
	Result *bean.FaceFusionTaskResult // 处理结果
	Error  error                      // 错误信息
}

// QueueStats 队列统计信息
type QueueStats struct {
	QueueSize      int   `json:"queue_size"`       // 当前队列大小
	ActiveRequests int   `json:"active_requests"`  // 当前活跃请求数
	TotalProcessed int64 `json:"total_processed"`  // 总处理数量
	TotalQueued    int64 `json:"total_queued"`     // 总排队数量
	TotalTimeout   int64 `json:"total_timeout"`    // 总超时数量
	TotalRejected  int64 `json:"total_rejected"`   // 总拒绝数量
	QPSLimit       int   `json:"qps_limit"`        // QPS限制
	MaxQueueSize   int   `json:"max_queue_size"`   // 最大队列大小
	EnableQPSLimit bool  `json:"enable_qps_limit"` // 是否启用QPS限制
}

var (
	globalQueue *QPSLimitedQueue
	queueOnce   sync.Once
)

// GetGlobalQueue 获取全局队列实例
func GetGlobalQueue() *QPSLimitedQueue {
	queueOnce.Do(func() {
		globalQueue = NewQPSLimitedQueue()
	})
	return globalQueue
}

// NewQPSLimitedQueue 创建新的QPS限制队列
func NewQPSLimitedQueue() *QPSLimitedQueue {
	conf := config.GlobConfig.FaceFusion

	// 设置默认值
	qpsLimit := conf.QPSLimit
	if qpsLimit <= 0 {
		qpsLimit = 18 // 默认18 QPS，为腾讯云20 QPS限制留安全余量
	}

	maxQueueSize := conf.MaxQueueSize
	if maxQueueSize < 0 {
		maxQueueSize = 1000 // 默认最大队列1000
	}
	// 允许maxQueueSize为0用于测试

	queueTimeoutSeconds := conf.QueueTimeoutSeconds
	if queueTimeoutSeconds <= 0 {
		queueTimeoutSeconds = 300 // 默认5分钟超时
	}

	enableQPSLimit := conf.EnableQPSLimit // 默认为false，需要显式配置

	queue := &QPSLimitedQueue{
		qpsLimit:            qpsLimit,
		maxQueueSize:        maxQueueSize,
		queueTimeoutSeconds: queueTimeoutSeconds,
		enableQPSLimit:      enableQPSLimit,
		queue:               make(chan *QueuedTask, maxQueueSize),
		lastRequestTime:     time.Now(),
	}

	// 启动处理协程
	go queue.processQueue()

	// 安全地记录日志（处理测试环境中logger可能为nil的情况）
	if logger.Logger != nil {
		logger.Logger.Infof("FaceFusion QPS queue initialized: qps_limit=%d, max_queue_size=%d, timeout=%ds, enabled=%v",
			qpsLimit, maxQueueSize, queueTimeoutSeconds, enableQPSLimit)
	}

	return queue
}

// Submit 提交任务到队列
func (q *QPSLimitedQueue) Submit(ctx context.Context, request *bean.FaceFusionTaskRequest) (*bean.FaceFusionTaskResult, error) {
	// 如果未启用QPS限制，直接处理
	if !q.enableQPSLimit {
		return q.processTaskDirectly(ctx, request)
	}

	// 检查队列是否已满
	if len(q.queue) >= q.maxQueueSize {
		q.mutex.Lock()
		q.totalRejected++
		q.mutex.Unlock()
		return nil, fmt.Errorf("queue is full, current size: %d, max size: %d", len(q.queue), q.maxQueueSize)
	}

	// 创建队列任务
	queuedTask := &QueuedTask{
		Request:     request,
		SubmittedAt: time.Now(),
		ResultChan:  make(chan *TaskResult, 1),
		Context:     ctx,
	}

	// 提交到队列
	select {
	case q.queue <- queuedTask:
		q.mutex.Lock()
		q.totalQueued++
		q.mutex.Unlock()
		if logger.Logger != nil {
			logger.Logger.InfofCtx(ctx, "FaceFusion task queued: task_id=%s, queue_size=%d", request.TaskID, len(q.queue))
		}
	case <-ctx.Done():
		return nil, ctx.Err()
	}

	// 等待结果
	timeout := time.Duration(q.queueTimeoutSeconds) * time.Second
	select {
	case result := <-queuedTask.ResultChan:
		if result.Error != nil {
			return nil, result.Error
		}
		return result.Result, nil
	case <-time.After(timeout):
		q.mutex.Lock()
		q.totalTimeout++
		q.mutex.Unlock()
		return nil, fmt.Errorf("task timeout after %v: task_id=%s", timeout, request.TaskID)
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

// processQueue 处理队列中的任务
func (q *QPSLimitedQueue) processQueue() {
	ticker := time.NewTicker(time.Second / time.Duration(q.qpsLimit)) // 根据QPS计算间隔
	defer ticker.Stop()

	for {
		select {
		case task := <-q.queue:
			// 等待QPS限制
			<-ticker.C

			// 异步处理任务
			go q.handleQueuedTask(task)

		case <-time.After(1 * time.Second):
			// 定期检查，避免协程阻塞
			continue
		}
	}
}

// handleQueuedTask 处理队列中的单个任务
func (q *QPSLimitedQueue) handleQueuedTask(task *QueuedTask) {
	defer func() {
		if r := recover(); r != nil {
			if logger.Logger != nil {
				logger.Logger.Errorf("FaceFusion task panic recovered: task_id=%s, panic=%v", task.Request.TaskID, r)
			}
			task.ResultChan <- &TaskResult{
				Error: fmt.Errorf("task processing panic: %v", r),
			}
		}
	}()

	q.mutex.Lock()
	q.activeRequests++
	q.mutex.Unlock()

	defer func() {
		q.mutex.Lock()
		q.activeRequests--
		q.totalProcessed++
		q.mutex.Unlock()
	}()

	// 检查任务是否已超时
	if time.Since(task.SubmittedAt) > time.Duration(q.queueTimeoutSeconds)*time.Second {
		task.ResultChan <- &TaskResult{
			Error: fmt.Errorf("task expired in queue: task_id=%s, queued_for=%v",
				task.Request.TaskID, time.Since(task.SubmittedAt)),
		}
		return
	}

	// 处理任务
	result, err := q.processTaskDirectly(task.Context, task.Request)
	task.ResultChan <- &TaskResult{
		Result: result,
		Error:  err,
	}
}

// processTaskDirectly 直接处理任务（不经过队列）
func (q *QPSLimitedQueue) processTaskDirectly(ctx context.Context, request *bean.FaceFusionTaskRequest) (*bean.FaceFusionTaskResult, error) {
	if logger.Logger != nil {
		logger.Logger.InfofCtx(ctx, "Processing face fusion task directly: task_id=%s", request.TaskID)
	}

	// 实际处理将在task handler中完成，这里只是队列管理
	// 避免循环依赖，不直接调用service层
	return &bean.FaceFusionTaskResult{
		TaskID:      request.TaskID,
		GameID:      request.GameID,
		UserID:      request.UserID,
		ModelID:     request.ModelID,
		ProjectID:   config.GlobConfig.FaceFusion.ProjectID,
		Status:      "queued",
		Message:     "Task queued for processing",
		ProcessedAt: time.Now(),
	}, nil
}

// GetStats 获取队列统计信息
func (q *QPSLimitedQueue) GetStats() *QueueStats {
	q.mutex.RLock()
	defer q.mutex.RUnlock()

	return &QueueStats{
		QueueSize:      len(q.queue),
		ActiveRequests: q.activeRequests,
		TotalProcessed: q.totalProcessed,
		TotalQueued:    q.totalQueued,
		TotalTimeout:   q.totalTimeout,
		TotalRejected:  q.totalRejected,
		QPSLimit:       q.qpsLimit,
		MaxQueueSize:   q.maxQueueSize,
		EnableQPSLimit: q.enableQPSLimit,
	}
}

// Shutdown 关闭队列
func (q *QPSLimitedQueue) Shutdown() {
	if logger.Logger != nil {
		logger.Logger.Info("Shutting down FaceFusion QPS queue...")
	}
	close(q.queue)
}
