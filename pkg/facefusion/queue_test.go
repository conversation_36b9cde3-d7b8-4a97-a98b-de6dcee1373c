package facefusion

import (
	"context"
	"fmt"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/stretchr/testify/assert"
)

func TestNewQPSLimitedQueue(t *testing.T) {
	// 设置测试配置
	config.GlobConfig = &config.Config{
		FaceFusion: config.FaceFusionConf{
			QPSLimit:            10,
			MaxQueueSize:        100,
			QueueTimeoutSeconds: 60,
			EnableQPSLimit:      true,
		},
	}

	queue := NewQPSLimitedQueue()
	assert.NotNil(t, queue)
	assert.Equal(t, 10, queue.qpsLimit)
	assert.Equal(t, 100, queue.maxQueueSize)
	assert.Equal(t, 60, queue.queueTimeoutSeconds)
	assert.True(t, queue.enableQPSLimit)
}

func TestQPSLimitedQueue_Submit_Disabled(t *testing.T) {
	// 设置测试配置 - 禁用QPS限制
	config.GlobConfig = &config.Config{
		FaceFusion: config.FaceFusionConf{
			EnableQPSLimit: false,
		},
	}

	queue := NewQPSLimitedQueue()
	ctx := context.Background()

	request := &bean.FaceFusionTaskRequest{
		TaskID:  "test-task-1",
		GameID:  "test-game",
		UserID:  "test-user",
		ModelID: "test-model",
		MergeInfos: []map[string]string{
			{"Url": "https://example.com/image.jpg"},
		},
		SubmittedAt: time.Now(),
	}

	result, err := queue.Submit(ctx, request)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "test-task-1", result.TaskID)
	assert.Equal(t, "queued", result.Status)
}

func TestQPSLimitedQueue_GetStats(t *testing.T) {
	config.GlobConfig = &config.Config{
		FaceFusion: config.FaceFusionConf{
			QPSLimit:            5,
			MaxQueueSize:        50,
			QueueTimeoutSeconds: 30,
			EnableQPSLimit:      true,
		},
	}

	queue := NewQPSLimitedQueue()
	stats := queue.GetStats()

	assert.NotNil(t, stats)
	assert.Equal(t, 5, stats.QPSLimit)
	assert.Equal(t, 50, stats.MaxQueueSize)
	assert.Equal(t, 0, stats.QueueSize)
	assert.Equal(t, 0, stats.ActiveRequests)
	assert.True(t, stats.EnableQPSLimit)
}

func TestQPSLimitedQueue_Submit_QueueFull(t *testing.T) {
	// 设置一个很小的队列用于测试
	config.GlobConfig = &config.Config{
		FaceFusion: config.FaceFusionConf{
			QPSLimit:            1,
			MaxQueueSize:        0, // 设置为0，这样任何提交都会立即失败
			QueueTimeoutSeconds: 1,
			EnableQPSLimit:      true,
		},
	}

	queue := NewQPSLimitedQueue()
	ctx := context.Background()

	request := &bean.FaceFusionTaskRequest{
		TaskID:  "test-task-1",
		GameID:  "test-game",
		UserID:  "test-user",
		ModelID: "test-model",
		MergeInfos: []map[string]string{
			{"Url": "https://example.com/image.jpg"},
		},
		SubmittedAt: time.Now(),
	}

	// 请求应该因为队列满而失败
	_, err := queue.Submit(ctx, request)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "queue is full")
}

func TestValidateFaceFusionTaskRequestNew(t *testing.T) {
	// 测试有效请求
	validRequest := &bean.FaceFusionTaskRequest{
		GameID:  "test-game",
		UserID:  "test-user",
		ModelID: "test-model",
		MergeInfos: []map[string]string{
			{"Url": "https://example.com/image.jpg"},
		},
	}

	err := validateFaceFusionTaskRequestNew(validRequest)
	assert.NoError(t, err)

	// 测试缺少GameID
	invalidRequest1 := &bean.FaceFusionTaskRequest{
		UserID:  "test-user",
		ModelID: "test-model",
		MergeInfos: []map[string]string{
			{"Url": "https://example.com/image.jpg"},
		},
	}

	err = validateFaceFusionTaskRequestNew(invalidRequest1)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "game_id is required")

	// 测试缺少MergeInfos
	invalidRequest2 := &bean.FaceFusionTaskRequest{
		GameID:     "test-game",
		UserID:     "test-user",
		ModelID:    "test-model",
		MergeInfos: []map[string]string{},
	}

	err = validateFaceFusionTaskRequestNew(invalidRequest2)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "merge_infos is required")

	// 测试MergeInfos格式错误
	invalidRequest3 := &bean.FaceFusionTaskRequest{
		GameID:  "test-game",
		UserID:  "test-user",
		ModelID: "test-model",
		MergeInfos: []map[string]string{
			{"InvalidKey": "https://example.com/image.jpg"},
		},
	}

	err = validateFaceFusionTaskRequestNew(invalidRequest3)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "missing 'Url' field")
}

func TestConvertToLegacyRequest(t *testing.T) {
	logoAdd := int64(1)
	fuseFaceDegree := int64(80)

	taskRequest := &bean.FaceFusionTaskRequest{
		GameID:         "test-game",
		UserID:         "test-user",
		ModelID:        "test-model",
		MergeInfos:     []map[string]string{{"Url": "https://example.com/image.jpg"}},
		FuseFaceDegree: &fuseFaceDegree,
		LogoAdd:        &logoAdd,
		LogoParam:      "test-logo",
		FuseParam:      "test-fuse",
		RspImgType:     "url",
	}

	legacyRequest := convertToLegacyRequest(taskRequest)

	assert.Equal(t, taskRequest.GameID, legacyRequest.GameID)
	assert.Equal(t, taskRequest.UserID, legacyRequest.UserID)
	assert.Equal(t, taskRequest.ModelID, legacyRequest.ModelID)
	assert.Equal(t, taskRequest.MergeInfos, legacyRequest.MergeInfos)
	assert.Equal(t, taskRequest.FuseFaceDegree, legacyRequest.FuseFaceDegree)
	assert.Equal(t, taskRequest.LogoAdd, legacyRequest.LogoAdd)
	assert.Equal(t, taskRequest.LogoParam, legacyRequest.LogoParam)
	assert.Equal(t, taskRequest.FuseParam, legacyRequest.FuseParam)
	assert.Equal(t, taskRequest.RspImgType, legacyRequest.RspImgType)
}

// 辅助函数，从task包中复制过来用于测试
func validateFaceFusionTaskRequestNew(req *bean.FaceFusionTaskRequest) error {
	if req.GameID == "" {
		return fmt.Errorf("game_id is required")
	}
	if req.ModelID == "" {
		return fmt.Errorf("model_id is required")
	}
	if req.UserID == "" {
		return fmt.Errorf("user_id is required")
	}
	if len(req.MergeInfos) == 0 {
		return fmt.Errorf("merge_infos is required")
	}
	for i, item := range req.MergeInfos {
		url, exists := item["Url"]
		if !exists {
			return fmt.Errorf("invalid merge_infos format: item %d missing 'Url' field", i)
		}
		if url == "" {
			return fmt.Errorf("invalid merge_infos format: item %d 'Url' cannot be empty", i)
		}
	}
	return nil
}

func convertToLegacyRequest(req *bean.FaceFusionTaskRequest) *bean.FaceFusionReq {
	return &bean.FaceFusionReq{
		GameID:            req.GameID,
		UserID:            req.UserID,
		ModelID:           req.ModelID,
		MergeInfos:        req.MergeInfos,
		FuseFaceDegree:    req.FuseFaceDegree,
		FuseProfileDegree: req.FuseProfileDegree,
		LogoAdd:           req.LogoAdd,
		LogoParam:         req.LogoParam,
		FuseParam:         req.FuseParam,
		RspImgType:        req.RspImgType,
	}
}
