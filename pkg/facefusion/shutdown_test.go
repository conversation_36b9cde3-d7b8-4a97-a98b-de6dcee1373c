package facefusion

import (
	"context"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/stretchr/testify/assert"
)

func TestQueueShutdown(t *testing.T) {
	// 设置测试配置
	config.GlobConfig = &config.Config{
		FaceFusion: config.FaceFusionConf{
			QPSLimit:            5,
			MaxQueueSize:        10,
			QueueTimeoutSeconds: 30,
			EnableQPSLimit:      true,
		},
	}

	// 创建队列
	queue := NewQPSLimitedQueue()
	assert.NotNil(t, queue)

	// 提交一些任务
	ctx := context.Background()
	for i := 0; i < 3; i++ {
		go func(taskID string) {
			request := &bean.FaceFusionTaskRequest{
				TaskID:  taskID,
				GameID:  "test-game",
				UserID:  "test-user",
				ModelID: "test-model",
			}
			_, err := queue.Submit(ctx, request)
			// 在关闭过程中可能会收到错误，这是正常的
			if err != nil {
				t.Logf("Task %s failed (expected during shutdown): %v", taskID, err)
			}
		}(string(rune('A' + i)))
	}

	// 等待一小段时间让任务进入队列
	time.Sleep(100 * time.Millisecond)

	// 测试优雅关闭
	start := time.Now()
	queue.Shutdown()
	duration := time.Since(start)

	// 关闭应该在合理时间内完成
	assert.Less(t, duration, 5*time.Second, "Shutdown should complete within 5 seconds")

	// 验证队列已关闭
	stats := queue.GetStats()
	t.Logf("Final stats: %+v", stats)
}

func TestQueueShutdownWithTimeout(t *testing.T) {
	// 设置测试配置
	config.GlobConfig = &config.Config{
		FaceFusion: config.FaceFusionConf{
			QPSLimit:            5,
			MaxQueueSize:        10,
			QueueTimeoutSeconds: 30,
			EnableQPSLimit:      true,
		},
	}

	// 创建队列
	queue := NewQPSLimitedQueue()
	assert.NotNil(t, queue)

	// 测试带超时的关闭
	start := time.Now()
	queue.ShutdownWithTimeout(1 * time.Second)
	duration := time.Since(start)

	// 关闭应该在超时时间内完成
	assert.Less(t, duration, 2*time.Second, "Shutdown with timeout should complete within timeout + buffer")
}

func TestQueueContextCancellation(t *testing.T) {
	// 设置测试配置
	config.GlobConfig = &config.Config{
		FaceFusion: config.FaceFusionConf{
			QPSLimit:            5,
			MaxQueueSize:        10,
			QueueTimeoutSeconds: 30,
			EnableQPSLimit:      true,
		},
	}

	// 创建队列
	queue := NewQPSLimitedQueue()
	assert.NotNil(t, queue)

	// 验证上下文和取消函数已正确初始化
	assert.NotNil(t, queue.ctx, "Queue context should be initialized")
	assert.NotNil(t, queue.cancel, "Queue cancel function should be initialized")

	// 测试上下文是否正常工作
	select {
	case <-queue.ctx.Done():
		t.Fatal("Queue context should not be cancelled initially")
	default:
		// 正常情况
	}

	// 调用取消函数
	queue.cancel()

	// 验证上下文已被取消
	select {
	case <-queue.ctx.Done():
		// 正常情况，上下文已被取消
	case <-time.After(100 * time.Millisecond):
		t.Fatal("Queue context should be cancelled after calling cancel()")
	}
}
