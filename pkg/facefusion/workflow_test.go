package facefusion

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/task"
	"github.com/hibiken/asynq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestFaceFusionWorkflow 测试完整的人脸融合工作流
// func TestFaceFusionWorkflow(t *testing.T) {
// 	ctx := context.Background()

// 	// 创建测试任务请求
// 	taskReq := &bean.FaceFusionTaskRequest{
// 		GameID:  "test-game-001",
// 		UserID:  "test-user-001",
// 		ModelID: "test-model-001",
// 		MergeInfos: []map[string]string{
// 			{"Url": "https://example.com/user-face.jpg"},
// 		},
// 		FuseFaceDegree:    nil, // 使用默认值
// 		FuseProfileDegree: nil, // 使用默认值
// 		LogoAdd:           nil, // 使用默认值
// 		LogoParam:         "",
// 		FuseParam:         "",
// 		RspImgType:        "base64",
// 		TaskID:            "test-task-001",
// 		SubmittedAt:       time.Now(),
// 		Priority:          0,
// 	}

// 	// 序列化任务数据
// 	taskData, err := json.Marshal(taskReq)
// 	require.NoError(t, err, "Failed to marshal task request")

// 	// 创建异步任务
// 	asynqTask := asynq.NewTask(task.TypeFaceFusion, taskData)

// 	// 测试任务处理
// 	err = task.HandleFaceFusionTask(ctx, asynqTask)

// 	// 注意：在实际测试环境中，这可能会失败，因为需要真实的腾讯云配置
// 	// 这里我们主要测试工作流的结构是否正确
// 	if err != nil {
// 		t.Logf("Task processing failed (expected in test environment): %v", err)
// 	}
// }

// TestFaceFusionService 测试人脸融合服务
func TestFaceFusionService(t *testing.T) {
	ctx := context.Background()
	faceFusionService := service.SingletonFaceFusionService()

	// 测试数据
	taskReq := &bean.FaceFusionTaskRequest{
		GameID:  "test-game-002",
		UserID:  "test-user-002",
		ModelID: "test-model-002",
		MergeInfos: []map[string]string{
			{"Url": "https://example.com/user-face.jpg"},
		},
		TaskID:      "test-task-002",
		SubmittedAt: time.Now(),
		Priority:    0,
	}

	// 测试创建记录
	record, err := faceFusionService.CreateFaceFusionRecord(ctx, taskReq)
	if err != nil {
		t.Logf("Create record failed (expected in test environment): %v", err)
		return
	}

	assert.NotNil(t, record)
	assert.Equal(t, taskReq.GameID, record.GameID)
	assert.Equal(t, taskReq.UserID, record.UserID)
	assert.Equal(t, taskReq.TaskID, record.TaskID)
	assert.Equal(t, "processing", record.Status)

	// 测试更新结果
	result := &bean.FaceFusionTaskResult{
		TaskID:      taskReq.TaskID,
		GameID:      taskReq.GameID,
		UserID:      taskReq.UserID,
		ModelID:     taskReq.ModelID,
		ProjectID:   "test-project-001",
		FusedImage:  "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...", // 示例base64
		RequestID:   "test-request-001",
		Status:      "success",
		Message:     "Face fusion completed successfully",
		ProcessedAt: time.Now(),
	}

	err = faceFusionService.UpdateFaceFusionResult(ctx, taskReq.TaskID, result)
	if err != nil {
		t.Logf("Update result failed (expected in test environment): %v", err)
	}

	// 测试查询记录
	retrievedRecord, err := faceFusionService.GetFaceFusionByTaskID(ctx, taskReq.TaskID)
	if err != nil {
		t.Logf("Get record failed (expected in test environment): %v", err)
		return
	}

	if retrievedRecord != nil {
		assert.Equal(t, taskReq.TaskID, retrievedRecord.TaskID)
		assert.Equal(t, "success", retrievedRecord.Status)
	}
}

// TestUploadService 测试OSS上传服务
func TestUploadService(t *testing.T) {
	ctx := context.Background()
	uploadService := service.SingletonUploadService()

	// 测试base64图片上传
	base64Data := "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
	fileName := "test-face-fusion.png"

	imageURL, err := uploadService.UploadBase64Image(ctx, base64Data, fileName)
	if err != nil {
		t.Logf("Upload base64 image failed (expected in test environment): %v", err)
		return
	}

	assert.NotEmpty(t, imageURL)
	t.Logf("Uploaded image URL: %s", imageURL)
}

// TestTaskValidation 测试任务验证
func TestTaskValidation(t *testing.T) {
	tests := []struct {
		name    string
		req     *bean.FaceFusionTaskRequest
		wantErr bool
	}{
		{
			name: "Valid request",
			req: &bean.FaceFusionTaskRequest{
				GameID:  "test-game",
				UserID:  "test-user",
				ModelID: "test-model",
				MergeInfos: []map[string]string{
					{"Url": "https://example.com/face.jpg"},
				},
				TaskID: "test-task",
			},
			wantErr: false,
		},
		{
			name: "Missing GameID",
			req: &bean.FaceFusionTaskRequest{
				UserID:  "test-user",
				ModelID: "test-model",
				MergeInfos: []map[string]string{
					{"Url": "https://example.com/face.jpg"},
				},
				TaskID: "test-task",
			},
			wantErr: true,
		},
		{
			name: "Missing UserID",
			req: &bean.FaceFusionTaskRequest{
				GameID:  "test-game",
				ModelID: "test-model",
				MergeInfos: []map[string]string{
					{"Url": "https://example.com/face.jpg"},
				},
				TaskID: "test-task",
			},
			wantErr: true,
		},
		{
			name: "Missing ModelID",
			req: &bean.FaceFusionTaskRequest{
				GameID: "test-game",
				UserID: "test-user",
				MergeInfos: []map[string]string{
					{"Url": "https://example.com/face.jpg"},
				},
				TaskID: "test-task",
			},
			wantErr: true,
		},
		{
			name: "Empty MergeInfos",
			req: &bean.FaceFusionTaskRequest{
				GameID:     "test-game",
				UserID:     "test-user",
				ModelID:    "test-model",
				MergeInfos: []map[string]string{},
				TaskID:     "test-task",
			},
			wantErr: true,
		},
		{
			name: "Invalid MergeInfos format",
			req: &bean.FaceFusionTaskRequest{
				GameID:  "test-game",
				UserID:  "test-user",
				ModelID: "test-model",
				MergeInfos: []map[string]string{
					{"InvalidKey": "https://example.com/face.jpg"},
				},
				TaskID: "test-task",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 这里我们测试验证逻辑，但由于validateFaceFusionTaskRequestNew函数可能不是公开的，
			// 我们通过创建任务来间接测试验证
			taskData, err := json.Marshal(tt.req)
			require.NoError(t, err)

			asynqTask := asynq.NewTask(task.TypeFaceFusion, taskData)
			err = task.HandleFaceFusionTask(context.Background(), asynqTask)

			if tt.wantErr {
				assert.Error(t, err, "Expected validation error for %s", tt.name)
			} else {
				// 在测试环境中，即使验证通过，后续步骤可能失败
				// 所以我们不严格检查错误，只记录结果
				t.Logf("Validation test for %s: err=%v", tt.name, err)
			}
		})
	}
}

// TestFaceFusionStats 测试统计功能
func TestFaceFusionStats(t *testing.T) {
	ctx := context.Background()
	faceFusionService := service.SingletonFaceFusionService()

	gameID := "test-game-stats"
	stats, err := faceFusionService.GetFaceFusionStats(ctx, gameID)
	if err != nil {
		t.Logf("Get stats failed (expected in test environment): %v", err)
		return
	}

	assert.NotNil(t, stats)
	assert.Contains(t, stats, "total")
	assert.Contains(t, stats, "success")
	assert.Contains(t, stats, "failed")
	assert.Contains(t, stats, "processing")

	t.Logf("Face fusion stats for game %s: %+v", gameID, stats)
}
